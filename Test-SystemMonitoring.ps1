# システム監視機能テストスクリプト
# Test script for system monitoring functionality

param(
    [Parameter(Mandatory = $false)]
    [int]$TestDurationSeconds = 30,
    
    [Parameter(Mandatory = $false)]
    [switch]$VerboseOutput
)

# テスト用のグローバル変数初期化
$Global:SystemMonitor = @{
    MemoryThresholdMB = 4096
    MaxMemoryUsageMB = 0
    MemoryCheckInterval = 10
    LastMemoryCheck = Get-Date
    NetworkAdapter = $null
    NetworkCounters = @{}
    TotalBytesUploaded = 0
    TotalBytesDownloaded = 0
    LastNetworkCheck = Get-Date
    MinDiskSpaceGB = 1
    MinAvailableMemoryGB = 2
    HealthCheckCounter = 0
    LastHealthCheck = Get-Date
    MonitorLogFile = ".\test-monitor_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
}

# ログ関数（簡易版）
function Write-TestLog {
    param(
        [string]$Message,
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "Error" { Write-Host $logMessage -ForegroundColor Red }
        "Warning" { Write-Host $logMessage -ForegroundColor Yellow }
        "Success" { Write-Host $logMessage -ForegroundColor Green }
        "Monitor" { Write-Host $logMessage -ForegroundColor Magenta }
        default { Write-Host $logMessage -ForegroundColor White }
    }
    
    # ファイル出力
    try {
        Add-Content -Path $Global:SystemMonitor.MonitorLogFile -Value $logMessage -Encoding UTF8 -ErrorAction SilentlyContinue
    }
    catch {
        # ログファイル書き込みエラーを無視
    }
}

# メモリ監視テスト関数
function Test-MemoryMonitoring {
    try {
        $currentProcess = Get-Process -Id $PID
        $memoryUsageMB = [math]::Round($currentProcess.WorkingSet64 / 1MB, 2)
        
        if ($memoryUsageMB -gt $Global:SystemMonitor.MaxMemoryUsageMB) {
            $Global:SystemMonitor.MaxMemoryUsageMB = $memoryUsageMB
        }
        
        Write-TestLog "現在のメモリ使用量: ${memoryUsageMB}MB (最大: $($Global:SystemMonitor.MaxMemoryUsageMB)MB)" "Monitor"
        
        if ($memoryUsageMB -gt $Global:SystemMonitor.MemoryThresholdMB) {
            Write-TestLog "メモリ使用量が閾値を超過しました。ガベージコレクションを実行します。" "Warning"
            [System.GC]::Collect()
            [System.GC]::WaitForPendingFinalizers()
            [System.GC]::Collect()
            Start-Sleep -Seconds 1
            
            $currentProcess = Get-Process -Id $PID
            $newMemoryUsageMB = [math]::Round($currentProcess.WorkingSet64 / 1MB, 2)
            $memoryReduced = $memoryUsageMB - $newMemoryUsageMB
            
            Write-TestLog "ガベージコレクション完了。メモリ使用量: ${newMemoryUsageMB}MB (削減: ${memoryReduced}MB)" "Success"
            return $true
        }
        
        return $false
    }
    catch {
        Write-TestLog "メモリ監視テストエラー: $($_.Exception.Message)" "Error"
        return $false
    }
}

# ネットワーク監視テスト関数
function Test-NetworkMonitoring {
    try {
        if (-not $Global:SystemMonitor.NetworkAdapter) {
            $activeAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
            if ($activeAdapters) {
                $Global:SystemMonitor.NetworkAdapter = $activeAdapters[0].Name
                Write-TestLog "アクティブなネットワークアダプターを検出: $($Global:SystemMonitor.NetworkAdapter)" "Monitor"
            }
        }
        
        if (-not $Global:SystemMonitor.NetworkAdapter) {
            Write-TestLog "アクティブなネットワークアダプターが見つかりません" "Warning"
            return $false
        }
        
        $adapterName = $Global:SystemMonitor.NetworkAdapter
        $currentCounters = Get-Counter -Counter @(
            "\Network Interface($adapterName)\Bytes Sent/sec",
            "\Network Interface($adapterName)\Bytes Received/sec"
        ) -ErrorAction SilentlyContinue
        
        if ($currentCounters) {
            $bytesSentPerSec = $currentCounters.CounterSamples | Where-Object { $_.Path -like "*Bytes Sent/sec*" } | Select-Object -ExpandProperty CookedValue
            $bytesReceivedPerSec = $currentCounters.CounterSamples | Where-Object { $_.Path -like "*Bytes Received/sec*" } | Select-Object -ExpandProperty CookedValue
            
            $uploadSpeedMbps = [math]::Round(($bytesSentPerSec * 8) / 1MB, 2)
            $downloadSpeedMbps = [math]::Round(($bytesReceivedPerSec * 8) / 1MB, 2)
            
            Write-TestLog "ネットワーク速度 - アップロード: ${uploadSpeedMbps}Mbps, ダウンロード: ${downloadSpeedMbps}Mbps" "Monitor"
            return $true
        }
        
        return $false
    }
    catch {
        Write-TestLog "ネットワーク監視テストエラー: $($_.Exception.Message)" "Error"
        return $false
    }
}

# システムリソーステスト関数
function Test-SystemResources {
    try {
        $currentDrive = (Get-Location).Drive.Name
        $driveInfo = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "${currentDrive}:" }
        
        if ($driveInfo) {
            $freeSpaceGB = [math]::Round($driveInfo.FreeSpace / 1GB, 2)
            Write-TestLog "利用可能ディスク容量: ${freeSpaceGB}GB" "Monitor"
        }
        
        $memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
        $availableMemoryGB = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
        Write-TestLog "利用可能システムメモリ: ${availableMemoryGB}GB" "Monitor"
        
        return $true
    }
    catch {
        Write-TestLog "システムリソーステストエラー: $($_.Exception.Message)" "Error"
        return $false
    }
}

# メインテスト実行
function Start-MonitoringTest {
    Write-TestLog "=== システム監視機能テスト開始 ===" "Info"
    Write-TestLog "テスト期間: $TestDurationSeconds 秒" "Info"
    Write-TestLog "監視ログファイル: $($Global:SystemMonitor.MonitorLogFile)" "Info"
    
    $startTime = Get-Date
    $testCount = 0
    
    while (((Get-Date) - $startTime).TotalSeconds -lt $TestDurationSeconds) {
        $testCount++
        Write-TestLog "--- テスト実行 #$testCount ---" "Info"
        
        # メモリ監視テスト
        $memoryResult = Test-MemoryMonitoring
        
        # ネットワーク監視テスト
        $networkResult = Test-NetworkMonitoring
        
        # システムリソーステスト
        $resourceResult = Test-SystemResources
        
        if ($VerboseOutput) {
            Write-TestLog "テスト結果 - メモリ: $memoryResult, ネットワーク: $networkResult, リソース: $resourceResult" "Info"
        }
        
        Start-Sleep -Seconds 5
    }
    
    $totalElapsed = (Get-Date) - $startTime
    Write-TestLog "=== テスト完了 ===" "Success"
    Write-TestLog "実行時間: $("{0:mm}:{0:ss}" -f $totalElapsed)" "Success"
    Write-TestLog "テスト実行回数: $testCount" "Success"
    Write-TestLog "最大メモリ使用量: $($Global:SystemMonitor.MaxMemoryUsageMB)MB" "Success"
    Write-TestLog "監視ログファイル: $($Global:SystemMonitor.MonitorLogFile)" "Info"
}

# テスト実行
if ($MyInvocation.InvocationName -ne '.') {
    Start-MonitoringTest
}
