{"systemMonitoring": {"memory": {"thresholdMB": 4096, "checkIntervalSeconds": 10, "enableAutoGC": true, "gcPauseSeconds": 2}, "network": {"enableMonitoring": true, "autoDetectAdapter": true, "preferredAdapter": null, "reportIntervalSeconds": 10}, "resources": {"minDiskSpaceGB": 1, "minAvailableMemoryGB": 2, "enablePreProcessCheck": true, "healthCheckIntervalMinutes": 5, "healthCheckFileInterval": 10}, "crashProtection": {"enableMemoryProtection": true, "enableResourceProtection": true, "enablePeriodicHealthCheck": true, "autoReduceConcurrency": true, "emergencyStopThreshold": 0.95}, "reporting": {"enableDetailedReports": true, "generateFinalReport": true, "monitorLogSeparate": true, "includeNetworkStats": true, "includeMemoryTrends": true}}, "repositories": [{"name": "example-repo", "source": "C:\\path\\to\\source", "sharepoint": {"siteUrl": "https://yourtenant.sharepoint.com/sites/yoursite", "library": "Shared Documents", "targetPath": "uploads/example-repo"}}]}