# PowerShell SharePoint アップロードスクリプト - システム監視機能

## 概要

このドキュメントでは、`Local-to-SharePoint-Upload-Windows0.ps1` スクリプトに追加された包括的なシステム監視および資源管理機能について説明します。

## 新機能一覧

### 1. メモリ監視システム
- **リアルタイムメモリ監視**: PowerShellプロセスのメモリ使用量を継続的に監視
- **自動ガベージコレクション**: 4GB閾値到達時に自動実行
- **メモリ使用量記録**: スクリプト実行中の最大メモリ使用量を追跡
- **メモリ使用傾向分析**: 時系列でのメモリ使用パターンを分析

### 2. ネットワーク帯域幅監視
- **自動アダプター検出**: アクティブなネットワークアダプターを自動識別
- **リアルタイム速度監視**: アップロード/ダウンロード速度をMbps単位で表示
- **累計転送量統計**: 総ネットワーク転送量をMB単位で記録
- **ネットワーク状態監視**: 接続の安定性と品質を評価

### 3. システムリソース保護機能
- **ディスク容量チェック**: 利用可能ディスク容量の監視
- **システムメモリ監視**: 全体的なシステムメモリ状況の確認
- **リソース不足警告**: 容量不足時の自動警告とアドバイス
- **処理停止保護**: 危険レベル到達時の自動処理停止

### 4. 智能監視調度
- **定期監視実行**: 10秒間隔での自動システムチェック
- **段階別レポート**: ファイル処理の各段階での詳細レポート生成
- **適応的監視**: 順次処理と並列処理に応じた監視戦略の調整
- **リアルタイム状態表示**: 現在の監視状況の即座表示

### 5. 防崩溃保護機能
- **メモリ過負荷保護**: 4GB超過時の自動ガベージコレクションと処理停止
- **定期ヘルスチェック**: 10ファイル処理毎または5分毎の状態確認
- **事前リソース検証**: 処理開始前の十分なリソース確認
- **智能降級**: リソース不足時の並列処理数自動調整

## 使用方法

### 基本的な実行
```powershell
# 標準実行（監視機能自動有効）
.\Local-to-SharePoint-Upload-Windows0.ps1 -ConfigFile "config.json" -CredentialsFile "credentials.json"

# 詳細監視ログ有効
.\Local-to-SharePoint-Upload-Windows0.ps1 -ConfigFile "config.json" -CredentialsFile "credentials.json" -VerboseLogging
```

### 監視機能のテスト
```powershell
# システム監視機能のテスト実行
.\Test-SystemMonitoring.ps1 -TestDurationSeconds 60 -VerboseOutput
```

## 監視パラメータ設定

### メモリ監視設定
- `MemoryThresholdMB`: メモリ閾値（デフォルト: 4096MB）
- `MemoryCheckInterval`: チェック間隔（デフォルト: 10秒）

### ネットワーク監視設定
- 自動アダプター検出が有効
- 転送速度とデータ量の継続的な追跡

### リソース保護設定
- `MinDiskSpaceGB`: 最小必要ディスク容量（デフォルト: 1GB）
- `MinAvailableMemoryGB`: 最小必要システムメモリ（デフォルト: 2GB）

## 生成されるレポート

### 1. リアルタイム監視ログ
- ファイル: `system-monitor_YYYYMMDD_HHMMSS.log`
- 内容: リアルタイムの監視データ

### 2. 最終監視レポート
- ファイル: `system-monitoring-report_YYYYMMDD_HHMMSS.txt`
- 内容: 包括的な実行統計と推奨事項

### 3. バッチ処理結果
- 成功ファイル: `success_*.csv`
- 失敗ファイル: `failure_*.csv`

## 監視データの解釈

### メモリ使用量
- **正常**: 閾値の80%以下
- **注意**: 閾値の80-95%
- **危険**: 閾値の95%以上（自動ガベージコレクション実行）

### ネットワーク転送
- アップロード/ダウンロード速度の表示
- 累計転送量の追跡
- 転送効率の分析

### システムリソース
- ディスク容量の十分性確認
- システムメモリの利用可能性確認
- 総合的なシステム健全性評価

## トラブルシューティング

### よくある問題と解決策

1. **メモリ不足エラー**
   - 並列処理数を減らす（`-MaxConcurrency` パラメータ）
   - バッチサイズを小さくする（`-BatchSize` パラメータ）

2. **ネットワーク監視エラー**
   - アクティブなネットワークアダプターの確認
   - 管理者権限での実行

3. **ディスク容量不足**
   - 不要ファイルの削除
   - 出力ディレクトリの変更

## 推奨設定

### 小規模環境（<1000ファイル）
- MaxConcurrency: 3
- BatchSize: 500
- ChunkSize: 25

### 中規模環境（1000-10000ファイル）
- MaxConcurrency: 5
- BatchSize: 1000
- ChunkSize: 50

### 大規模環境（>10000ファイル）
- MaxConcurrency: 8
- BatchSize: 2000
- ChunkSize: 100

## 注意事項

1. **管理者権限**: ネットワーク監視機能には管理者権限が必要な場合があります
2. **PowerShell バージョン**: 並列処理にはPowerShell 7.0以上を推奨
3. **リソース要件**: 最低4GBのRAMと1GBの利用可能ディスク容量が必要
4. **ネットワーク環境**: 安定したインターネット接続が必要

## サポート

問題が発生した場合は、以下のログファイルを確認してください：
- メインログ: `local-to-sharepoint_YYYYMMDD_HHMMSS.log`
- 監視ログ: `system-monitor_YYYYMMDD_HHMMSS.log`
- 最終レポート: `system-monitoring-report_YYYYMMDD_HHMMSS.txt`
