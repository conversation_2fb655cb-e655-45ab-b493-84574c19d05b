# ローカルディレクトリからSharePointへのファイルアップロードスクリプト（Windows最適化版）

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigFile = ".\config.json",

    [Parameter(Mandatory = $false)]
    [switch]$ResumeMode,

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".\output",

    [Parameter(Mandatory = $false)]
    [int]$BatchSize = 1000,

    [Parameter(Mandatory = $false)]
    [switch]$VerboseLogging,

    [Parameter(Mandatory = $false)]
    [string]$CredentialsFile = ".\credentials.json",

    [Parameter(Mandatory = $false)]
    [int]$MaxConcurrency = 5,

    [Parameter(Mandatory = $false)]
    [int]$ChunkSize = 50
)

# Windows環境チェック
if ($PSVersionTable.Platform -eq "Unix") {
    throw "This script is optimized for Windows environment only."
}

# グローバル変数の初期化
$Global:StartTime = Get-Date
$Global:LogFile = ".\local-to-sharepoint_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
$Global:ProcessedReposFile = ".\processed_repos.txt"
$Global:CurrentBatchSuccess = @()
$Global:CurrentBatchFailure = @()
$Global:BatchCounter = 0

# システム監視用グローバル変数
$Global:SystemMonitor = @{
    # メモリ監視設定
    MemoryThresholdMB = 4096  # 4GB閾値
    MaxMemoryUsageMB = 0      # 最大メモリ使用量記録
    MemoryCheckInterval = 10  # 10秒間隔でチェック
    LastMemoryCheck = Get-Date

    # ネットワーク監視設定
    NetworkAdapter = $null    # アクティブなネットワークアダプター
    NetworkCounters = @{}     # ネットワークカウンター
    TotalBytesUploaded = 0    # 累計アップロード量
    TotalBytesDownloaded = 0  # 累計ダウンロード量
    LastNetworkCheck = Get-Date

    # システムリソース監視
    MinDiskSpaceGB = 1        # 最小必要ディスク容量（GB）
    MinAvailableMemoryGB = 2  # 最小必要メモリ（GB）
    HealthCheckCounter = 0    # ヘルスチェックカウンター
    LastHealthCheck = Get-Date

    # 監視ログファイル
    MonitorLogFile = ".\system-monitor_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
}

# ログ関数
function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("Info", "Warning", "Error", "Success", "Debug", "Monitor")]
        [string]$Level = "Info",

        [Parameter(Mandatory = $false)]
        [switch]$VerboseOnly,

        [Parameter(Mandatory = $false)]
        [switch]$NoFileOutput,

        [Parameter(Mandatory = $false)]
        [switch]$MonitorOnly
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"

    # コンソール出力の制御
    $shouldOutputToConsole = $true
    if ($VerboseOnly -and -not $VerboseLogging) {
        $shouldOutputToConsole = $false
    }
    if ($MonitorOnly -and $Level -ne "Monitor") {
        $shouldOutputToConsole = $false
    }

    if ($shouldOutputToConsole) {
        switch ($Level) {
            "Error" {
                Write-Host $logMessage -ForegroundColor Red
            }
            "Warning" { Write-Host $logMessage -ForegroundColor Yellow }
            "Success" { Write-Host $logMessage -ForegroundColor Green }
            "Info" { Write-Host $logMessage -ForegroundColor White }
            "Monitor" { Write-Host $logMessage -ForegroundColor Magenta }
            "Debug" {
                if ($VerboseLogging) {
                    Write-Host $logMessage -ForegroundColor Cyan
                }
            }
            default { Write-Host $logMessage }
        }
    }

    # ファイル出力の制御
    if (-not $NoFileOutput) {
        $shouldOutputToFile = $true
        if ($Level -eq "Debug" -and -not $VerboseLogging) {
            $shouldOutputToFile = $false
        }

        if ($shouldOutputToFile) {
            try {
                Add-Content -Path $Global:LogFile -Value $logMessage -Encoding UTF8 -ErrorAction SilentlyContinue

                # 監視ログの場合は専用ファイルにも出力
                if ($Level -eq "Monitor" -or $MonitorOnly) {
                    Add-Content -Path $Global:SystemMonitor.MonitorLogFile -Value $logMessage -Encoding UTF8 -ErrorAction SilentlyContinue
                }
            }
            catch {
                # ログファイル書き込みエラーを無視
            }
        }
    }
}

# Windowsパス最適化関数
function Resolve-WindowsPath {
    param([string]$Path)

    try {
        # 環境変数の展開
        $expandedPath = [System.Environment]::ExpandEnvironmentVariables($Path)

        # 絶対パスに変換
        if (-not [System.IO.Path]::IsPathRooted($expandedPath)) {
            $expandedPath = Join-Path (Get-Location) $expandedPath
        }

        # パスの正規化
        return [System.IO.Path]::GetFullPath($expandedPath)
    }
    catch {
        Write-Log "Failed to resolve path: $Path" "Warning"
        return $Path
    }
}

# システム監視機能：メモリ監視システム
function Start-MemoryMonitoring {
    <#
    .SYNOPSIS
    メモリ使用量をリアルタイムで監視し、閾値を超えた場合に自動的にガベージコレクションを実行

    .DESCRIPTION
    現在のPowerShellプロセスのメモリ使用量を監視し、設定された閾値（4GB）を超えた場合に
    自動的にガベージコレクションを実行してメモリを解放する
    #>

    try {
        $currentProcess = Get-Process -Id $PID
        $memoryUsageMB = [math]::Round($currentProcess.WorkingSet64 / 1MB, 2)

        # 最大メモリ使用量の更新
        if ($memoryUsageMB -gt $Global:SystemMonitor.MaxMemoryUsageMB) {
            $Global:SystemMonitor.MaxMemoryUsageMB = $memoryUsageMB
        }

        Write-Log "現在のメモリ使用量: ${memoryUsageMB}MB (最大: $($Global:SystemMonitor.MaxMemoryUsageMB)MB)" "Monitor" -MonitorOnly

        # 閾値チェック
        if ($memoryUsageMB -gt $Global:SystemMonitor.MemoryThresholdMB) {
            Write-Log "メモリ使用量が閾値($($Global:SystemMonitor.MemoryThresholdMB)MB)を超過しました。ガベージコレクションを実行します。" "Warning"

            # ガベージコレクション実行
            [System.GC]::Collect()
            [System.GC]::WaitForPendingFinalizers()
            [System.GC]::Collect()

            # 2秒間の処理停止
            Start-Sleep -Seconds 2

            # メモリ使用量再確認
            $currentProcess = Get-Process -Id $PID
            $newMemoryUsageMB = [math]::Round($currentProcess.WorkingSet64 / 1MB, 2)
            $memoryReduced = $memoryUsageMB - $newMemoryUsageMB

            Write-Log "ガベージコレクション完了。メモリ使用量: ${newMemoryUsageMB}MB (削減: ${memoryReduced}MB)" "Success"

            return @{
                MemoryUsageMB = $newMemoryUsageMB
                MemoryReduced = $memoryReduced
                GCExecuted = $true
            }
        }

        return @{
            MemoryUsageMB = $memoryUsageMB
            MemoryReduced = 0
            GCExecuted = $false
        }
    }
    catch {
        Write-Log "メモリ監視エラー: $($_.Exception.Message)" "Error"
        return @{
            MemoryUsageMB = 0
            MemoryReduced = 0
            GCExecuted = $false
        }
    }
}



# システム監視機能：ネットワーク帯域幅監視
function Start-NetworkMonitoring {
    <#
    .SYNOPSIS
    ネットワーク帯域幅とデータ転送量をリアルタイムで監視

    .DESCRIPTION
    アクティブなネットワークアダプターを自動検出し、ネットワーク転送速度と累計転送量を監視する
    #>

    try {
        # アクティブなネットワークアダプターの検出
        if (-not $Global:SystemMonitor.NetworkAdapter) {
            $activeAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" -and $_.MediaType -eq "802.3" }
            if ($activeAdapters) {
                $Global:SystemMonitor.NetworkAdapter = $activeAdapters[0].Name
                Write-Log "アクティブなネットワークアダプターを検出: $($Global:SystemMonitor.NetworkAdapter)" "Monitor" -MonitorOnly
            }
        }

        if (-not $Global:SystemMonitor.NetworkAdapter) {
            return @{
                UploadSpeedMbps = 0
                DownloadSpeedMbps = 0
                TotalUploadMB = 0
                TotalDownloadMB = 0
            }
        }

        # ネットワークカウンターの取得
        $adapterName = $Global:SystemMonitor.NetworkAdapter
        $currentCounters = Get-Counter -Counter @(
            "\Network Interface($adapterName)\Bytes Sent/sec",
            "\Network Interface($adapterName)\Bytes Received/sec"
        ) -ErrorAction SilentlyContinue

        if ($currentCounters) {
            $bytesSentPerSec = $currentCounters.CounterSamples | Where-Object { $_.Path -like "*Bytes Sent/sec*" } | Select-Object -ExpandProperty CookedValue
            $bytesReceivedPerSec = $currentCounters.CounterSamples | Where-Object { $_.Path -like "*Bytes Received/sec*" } | Select-Object -ExpandProperty CookedValue

            # Mbpsに変換
            $uploadSpeedMbps = [math]::Round(($bytesSentPerSec * 8) / 1MB, 2)
            $downloadSpeedMbps = [math]::Round(($bytesReceivedPerSec * 8) / 1MB, 2)

            # 累計転送量の更新
            $timeDiff = ((Get-Date) - $Global:SystemMonitor.LastNetworkCheck).TotalSeconds
            if ($timeDiff -gt 0) {
                $Global:SystemMonitor.TotalBytesUploaded += ($bytesSentPerSec * $timeDiff)
                $Global:SystemMonitor.TotalBytesDownloaded += ($bytesReceivedPerSec * $timeDiff)
            }

            $totalUploadMB = [math]::Round($Global:SystemMonitor.TotalBytesUploaded / 1MB, 2)
            $totalDownloadMB = [math]::Round($Global:SystemMonitor.TotalBytesDownloaded / 1MB, 2)

            Write-Log "ネットワーク速度 - アップロード: ${uploadSpeedMbps}Mbps, ダウンロード: ${downloadSpeedMbps}Mbps" "Monitor" -MonitorOnly
            Write-Log "累計転送量 - アップロード: ${totalUploadMB}MB, ダウンロード: ${totalDownloadMB}MB" "Monitor" -MonitorOnly

            $Global:SystemMonitor.LastNetworkCheck = Get-Date

            return @{
                UploadSpeedMbps = $uploadSpeedMbps
                DownloadSpeedMbps = $downloadSpeedMbps
                TotalUploadMB = $totalUploadMB
                TotalDownloadMB = $totalDownloadMB
            }
        }

        return @{
            UploadSpeedMbps = 0
            DownloadSpeedMbps = 0
            TotalUploadMB = 0
            TotalDownloadMB = 0
        }
    }
    catch {
        Write-Log "ネットワーク監視エラー: $($_.Exception.Message)" "Error"
        return @{
            UploadSpeedMbps = 0
            DownloadSpeedMbps = 0
            TotalUploadMB = 0
            TotalDownloadMB = 0
        }
    }
}

# 証明書認証情報の読み取り
function Read-CertificateCredentials {
    param([string]$FilePath)

    try {
        if (-not (Test-Path $FilePath)) {
            throw "Credentials file not found: $FilePath"
        }

        $credentialsContent = Get-Content -Path $FilePath -Raw -Encoding UTF8
        $credentialsJson = $credentialsContent | ConvertFrom-Json

        if (-not $credentialsJson.clientId -or -not $credentialsJson.certificatePath -or -not $credentialsJson.tenantId) {
            throw "Missing required fields: clientId, certificatePath, tenantId"
        }

        if (-not (Test-Path $credentialsJson.certificatePath)) {
            throw "Certificate file not found: $($credentialsJson.certificatePath)"
        }

        Write-Log "Certificate credentials loaded successfully" "Success"

        return @{
            ClientId = $credentialsJson.clientId
            CertificatePath = $credentialsJson.certificatePath
            CertificatePassword = $credentialsJson.certificatePassword
            TenantId = $credentialsJson.tenantId
        }
    }
    catch {
        Write-Log "Failed to read credentials: $($_.Exception.Message)" "Error"
        throw
    }
}

# システム監視機能：システムリソース保護機能
function Test-SystemResources {
    <#
    .SYNOPSIS
    システムリソースの状態をチェックし、不足している場合は警告を発する

    .DESCRIPTION
    ディスク容量、システムメモリ、その他のリソースをチェックして、
    ファイル処理を安全に継続できるかを判定する
    #>

    try {
        $resourceStatus = @{
            DiskSpaceOK = $true
            MemoryOK = $true
            OverallOK = $true
            Warnings = @()
            Recommendations = @()
        }

        # ディスク容量チェック
        $currentDrive = (Get-Location).Drive.Name
        $driveInfo = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "${currentDrive}:" }

        if ($driveInfo) {
            $freeSpaceGB = [math]::Round($driveInfo.FreeSpace / 1GB, 2)
            Write-Log "利用可能ディスク容量: ${freeSpaceGB}GB (最小必要: $($Global:SystemMonitor.MinDiskSpaceGB)GB)" "Monitor" -MonitorOnly

            if ($freeSpaceGB -lt $Global:SystemMonitor.MinDiskSpaceGB) {
                $resourceStatus.DiskSpaceOK = $false
                $resourceStatus.Warnings += "ディスク容量不足: ${freeSpaceGB}GB (必要: $($Global:SystemMonitor.MinDiskSpaceGB)GB)"
                $resourceStatus.Recommendations += "不要なファイルを削除してディスク容量を確保してください"
            }
        }

        # システムメモリチェック
        $memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
        $availableMemoryGB = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
        Write-Log "利用可能システムメモリ: ${availableMemoryGB}GB (最小必要: $($Global:SystemMonitor.MinAvailableMemoryGB)GB)" "Monitor" -MonitorOnly

        if ($availableMemoryGB -lt $Global:SystemMonitor.MinAvailableMemoryGB) {
            $resourceStatus.MemoryOK = $false
            $resourceStatus.Warnings += "システムメモリ不足: ${availableMemoryGB}GB (必要: $($Global:SystemMonitor.MinAvailableMemoryGB)GB)"
            $resourceStatus.Recommendations += "他のアプリケーションを終了するか、並行処理数を減らしてください"
        }

        # 総合判定
        $resourceStatus.OverallOK = $resourceStatus.DiskSpaceOK -and $resourceStatus.MemoryOK

        # 警告の出力
        foreach ($warning in $resourceStatus.Warnings) {
            Write-Log $warning "Warning"
        }

        foreach ($recommendation in $resourceStatus.Recommendations) {
            Write-Log "推奨事項: $recommendation" "Info"
        }

        return $resourceStatus
    }
    catch {
        Write-Log "システムリソースチェックエラー: $($_.Exception.Message)" "Error"
        return @{
            DiskSpaceOK = $false
            MemoryOK = $false
            OverallOK = $false
            Warnings = @("システムリソースチェックに失敗しました")
            Recommendations = @()
        }
    }
}

# システム監視機能：統合監視システム
function Invoke-SystemMonitoring {
    <#
    .SYNOPSIS
    統合システム監視を実行し、メモリ、ネットワーク、リソースの状態を一括チェック

    .DESCRIPTION
    定期的に実行される統合監視機能。メモリ、ネットワーク、システムリソースの
    状態をチェックし、必要に応じて保護措置を実行する
    #>

    param(
        [switch]$Force,
        [switch]$DetailedReport
    )

    try {
        $currentTime = Get-Date
        $timeSinceLastCheck = ($currentTime - $Global:SystemMonitor.LastMemoryCheck).TotalSeconds

        # 監視間隔チェック（強制実行でない場合）
        if (-not $Force -and $timeSinceLastCheck -lt $Global:SystemMonitor.MemoryCheckInterval) {
            return $null
        }

        Write-Log "=== システム監視実行 ===" "Monitor" -MonitorOnly

        # メモリ監視
        $memoryStatus = Start-MemoryMonitoring

        # ネットワーク監視
        $networkStatus = Start-NetworkMonitoring

        # システムリソースチェック
        $resourceStatus = Test-SystemResources

        # 統合レポート作成
        $monitoringReport = @{
            Timestamp = $currentTime
            Memory = $memoryStatus
            Network = $networkStatus
            Resources = $resourceStatus
            HealthCheckCount = ++$Global:SystemMonitor.HealthCheckCounter
        }

        # 詳細レポートの出力
        if ($DetailedReport -or $VerboseLogging) {
            Write-Log "--- 監視レポート #$($monitoringReport.HealthCheckCount) ---" "Monitor"
            Write-Log "メモリ使用量: $($memoryStatus.MemoryUsageMB)MB" "Monitor"
            Write-Log "ネットワーク速度: ↑$($networkStatus.UploadSpeedMbps)Mbps ↓$($networkStatus.DownloadSpeedMbps)Mbps" "Monitor"
            Write-Log "累計転送量: ↑$($networkStatus.TotalUploadMB)MB ↓$($networkStatus.TotalDownloadMB)MB" "Monitor"
            Write-Log "リソース状態: $(if($resourceStatus.OverallOK){'正常'}else{'警告あり'})" "Monitor"
        }

        $Global:SystemMonitor.LastMemoryCheck = $currentTime
        $Global:SystemMonitor.LastHealthCheck = $currentTime

        return $monitoringReport
    }
    catch {
        Write-Log "統合システム監視エラー: $($_.Exception.Message)" "Error"
        return $null
    }
}

# 設定ファイルの読み取り
function Read-ConfigurationFile {
    param([string]$FilePath)

    try {
        if (-not (Test-Path $FilePath)) {
            throw "Configuration file not found: $FilePath"
        }

        Write-Log "Reading configuration file: $FilePath" "Info"

        $configs = @()
        $fileExtension = [System.IO.Path]::GetExtension($FilePath).ToLower()

        if ($fileExtension -eq ".json") {
            # JSON形式
            $jsonContent = Get-Content $FilePath -Raw -Encoding UTF8
            $jsonConfig = $jsonContent | ConvertFrom-Json

            if ($jsonConfig.repositories) {
                foreach ($repo in $jsonConfig.repositories) {
                    $config = [PSCustomObject]@{
                        RepositoryName = $repo.name
                        SourceDirectory = $repo.source
                        SharePointSiteUrl = $repo.sharepoint.siteUrl
                        SharePointLibrary = $repo.sharepoint.library
                        TargetDirectory = $repo.sharepoint.targetPath
                    }
                    $configs += $config
                }
            }
        }
        else {
            # CSV形式
            $content = Get-Content $FilePath -Encoding UTF8
            foreach ($line in $content) {
                $line = $line.Trim()
                if ($line -eq "" -or $line.StartsWith("#")) {
                    continue
                }

                $parts = $line -split ","
                if ($parts.Count -ge 5) {
                    $config = [PSCustomObject]@{
                        RepositoryName = $parts[0].Trim()
                        SourceDirectory = $parts[1].Trim()
                        SharePointSiteUrl = $parts[2].Trim()
                        SharePointLibrary = $parts[3].Trim()
                        TargetDirectory = $parts[4].Trim()
                    }
                    $configs += $config
                }
            }
        }

        Write-Log "Loaded $($configs.Count) configurations" "Success"
        return $configs
    }
    catch {
        Write-Log "Failed to read configuration: $($_.Exception.Message)" "Error"
        return @()
    }
}

# システム監視機能：防崩溃保護機能
function Invoke-CrashProtection {
    <#
    .SYNOPSIS
    システムクラッシュを防ぐための保護機能を実行

    .DESCRIPTION
    メモリ過負荷、リソース不足などの状況を検出し、
    システムの安定性を保つための保護措置を実行する
    #>

    param(
        [int]$ProcessedFileCount = 0,
        [switch]$PreProcessCheck
    )

    try {
        $protectionResult = @{
            ProtectionExecuted = $false
            RecommendedAction = "continue"
            Message = ""
        }

        # 事前処理チェック（ファイル処理開始前）
        if ($PreProcessCheck) {
            Write-Log "事前リソースチェックを実行中..." "Info"
            $resourceStatus = Test-SystemResources

            if (-not $resourceStatus.OverallOK) {
                $protectionResult.ProtectionExecuted = $true
                $protectionResult.RecommendedAction = "abort"
                $protectionResult.Message = "システムリソースが不足しています。処理を中止することを推奨します。"

                Write-Log $protectionResult.Message "Warning"
                foreach ($warning in $resourceStatus.Warnings) {
                    Write-Log "詳細: $warning" "Warning"
                }

                return $protectionResult
            }
        }

        # 定期ヘルスチェック（10ファイルごとまたは5分ごと）
        $timeSinceLastCheck = ((Get-Date) - $Global:SystemMonitor.LastHealthCheck).TotalMinutes
        $shouldPerformHealthCheck = ($ProcessedFileCount -gt 0 -and $ProcessedFileCount % 10 -eq 0) -or $timeSinceLastCheck -ge 5

        if ($shouldPerformHealthCheck) {
            Write-Log "定期ヘルスチェックを実行中... (処理済みファイル: $ProcessedFileCount)" "Info"

            # 統合監視実行
            $monitoringReport = Invoke-SystemMonitoring -Force

            if ($monitoringReport) {
                # メモリ過負荷チェック
                if ($monitoringReport.Memory.MemoryUsageMB -gt ($Global:SystemMonitor.MemoryThresholdMB * 0.9)) {
                    $protectionResult.ProtectionExecuted = $true
                    $protectionResult.RecommendedAction = "pause"
                    $protectionResult.Message = "メモリ使用量が危険レベルに達しています。処理を一時停止します。"

                    Write-Log $protectionResult.Message "Warning"

                    # 強制ガベージコレクション
                    [System.GC]::Collect()
                    [System.GC]::WaitForPendingFinalizers()
                    [System.GC]::Collect()
                    Start-Sleep -Seconds 3

                    return $protectionResult
                }

                # リソース不足チェック
                if (-not $monitoringReport.Resources.OverallOK) {
                    $protectionResult.ProtectionExecuted = $true
                    $protectionResult.RecommendedAction = "reduce_concurrency"
                    $protectionResult.Message = "システムリソースが不足しています。並行処理数を減らすことを推奨します。"

                    Write-Log $protectionResult.Message "Warning"
                    return $protectionResult
                }
            }
        }

        return $protectionResult
    }
    catch {
        Write-Log "クラッシュ保護機能エラー: $($_.Exception.Message)" "Error"
        return @{
            ProtectionExecuted = $false
            RecommendedAction = "continue"
            Message = "保護機能でエラーが発生しましたが、処理を継続します"
        }
    }
}

# SharePoint接続関数（監視機能統合版）
function Connect-SharePointSite {
    param(
        [string]$SiteUrl,
        [hashtable]$CertCredentials,
        [int]$MaxRetries = 3
    )

    # 接続前のシステム監視
    $monitoringReport = Invoke-SystemMonitoring -Force
    if ($monitoringReport) {
        Write-Log "SharePoint接続前のシステム状態チェック完了" "Info"
    }

    $attempt = 0
    while ($attempt -lt $MaxRetries) {
        $attempt++

        try {
            Write-Log "Connecting to SharePoint: $SiteUrl (Attempt $attempt/$MaxRetries)" "Info"

            # 証明書パスワード処理
            $securePassword = $null
            if ($CertCredentials.CertificatePassword) {
                if ($CertCredentials.CertificatePassword -is [SecureString]) {
                    $securePassword = $CertCredentials.CertificatePassword
                }
                else {
                    $securePassword = ConvertTo-SecureString -String $CertCredentials.CertificatePassword -AsPlainText -Force
                }
            }

            # SharePoint接続
            $connectParams = @{
                Url = $SiteUrl
                ClientId = $CertCredentials.ClientId
                CertificatePath = $CertCredentials.CertificatePath
                Tenant = $CertCredentials.TenantId
                WarningAction = 'SilentlyContinue'
                ErrorAction = 'Stop'
            }

            if ($securePassword) {
                $connectParams.CertificatePassword = $securePassword
            }

            Connect-PnPOnline @connectParams

            # 接続検証
            $null = Get-PnPWeb -ErrorAction Stop

            Write-Log "SharePoint connection successful" "Success"

            # 接続成功後のネットワーク監視
            $networkStatus = Start-NetworkMonitoring
            Write-Log "ネットワーク接続状態: アップロード速度 $($networkStatus.UploadSpeedMbps)Mbps" "Info"

            return $true
        }
        catch {
            Write-Log "Connection attempt $attempt failed: $($_.Exception.Message)" "Warning"

            if ($attempt -lt $MaxRetries) {
                Start-Sleep -Seconds (2 * $attempt)
            }
        }
    }

    Write-Log "Failed to connect after $MaxRetries attempts" "Error"
    return $false
}


# ソースディレクトリから直接ファイルを取得
function Get-SourceFiles {
    param(
        [string]$RepositoryName,
        [string]$SourceDirectory
    )

    try {
        Write-Log "Reading files from source directory: $RepositoryName" "Info"
        Write-Log "Source Directory: $SourceDirectory" "Debug" -VerboseOnly

        # パスの解決と正規化
        $resolvedPath = Resolve-WindowsPath -Path $SourceDirectory

        # ソースディレクトリの存在確認
        if (-not (Test-Path $resolvedPath -PathType Container)) {
            throw "Source directory does not exist: $resolvedPath"
        }

        Write-Log "Resolved source path: $resolvedPath" "Debug" -VerboseOnly

        # ディレクトリ内のファイルを再帰的に取得
        $files = Get-ChildItem -Path $resolvedPath -Recurse -File -ErrorAction Stop
        Write-Log "$($files.Count)個のファイルを検出しました" "Success"

        return @{
            Success = $true
            SourceDirectory = $resolvedPath
            Files = $files
            IsTemporary = $false  # 一時ディレクトリではないことを示す
        }
    }
    catch {
        Write-Log "ソースディレクトリからのファイル取得が失敗しました: $($_.Exception.Message)" "Error"
        Write-Log "エラー詳細: $($_.Exception.GetType().Name)" "Debug" -VerboseOnly

        return @{
            Success = $false
            SourceDirectory = $null
            Files = @()
            IsTemporary = $false
        }
    }
}

# SharePointフォルダ作成
function Ensure-SharePointFolder {
    param(
        [string]$FolderPath,
        [string]$LibraryName = "Shared Documents"
    )

    try {
        $folderOnly = Split-Path $FolderPath -Parent
        if ([string]::IsNullOrEmpty($folderOnly) -or $folderOnly -in @(".", "/", "\")) {
            return $true
        }

        $normalizedPath = ($folderOnly -replace "\\", "/" -replace "^/", "").Trim()
        if ([string]::IsNullOrEmpty($normalizedPath)) {
            return $true
        }

        # フォルダが存在するかチェック
        try {
            # Get-PnPFolderWithExplicitLoad を使用してServerRelativePathを明示的に取得
            $folder = Get-PnPFolder -Url $normalizedPath -Includes ServerRelativeUrl -ErrorAction Stop
            if ($folder) {
                Write-Log "Folder already exists: $normalizedPath" "Debug" -VerboseOnly
                return $true
            }
        }
        catch {
            # フォルダが存在しない、作成が必要
            Write-Log "Folder does not exist, creating: $normalizedPath" "Debug" -VerboseOnly
        }

        # 段階的にフォルダを作成
        $pathParts = $normalizedPath -split "/" | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
        $currentPath = ""

        foreach ($part in $pathParts) {
            $currentPath = if ($currentPath -eq "") { $part } else { "$currentPath/$part" }

            try {
                # ServerRelativeUrlを明示的に含めてフォルダの存在確認
                $existingFolder = Get-PnPFolder -Url $currentPath -Includes ServerRelativeUrl -ErrorAction Stop
                if ($existingFolder) {
                    Write-Log "Folder part exists: $currentPath" "Debug" -VerboseOnly
                    continue
                }
            }
            catch {
                # フォルダが存在しないので作成する
                $parentPath = if ($currentPath.Contains("/")) {
                    $currentPath.Substring(0, $currentPath.LastIndexOf("/"))
                } else {
                    ""
                }

                $folderToCreate = if ($parentPath -eq "") {
                    $LibraryName
                } else {
                    "$LibraryName/$parentPath"
                }

                try {
                    Write-Log "Creating folder: $part in $folderToCreate" "Debug" -VerboseOnly
                    $newFolder = Add-PnPFolder -Name $part -Folder $folderToCreate -ErrorAction Stop
                    
                    # 作成したフォルダの確認（ServerRelativeUrlを明示的に取得）
                    if ($newFolder) {
                        $null = Get-PnPFolder -Url $currentPath -Includes ServerRelativeUrl -ErrorAction SilentlyContinue
                        Write-Log "Successfully created folder: $currentPath" "Debug" -VerboseOnly
                    }
                }
                catch {
                    if ($_.Exception.Message -notmatch "already exists|既にあります|already exist") {
                        Write-Log "Error creating folder $part in ${folderToCreate}: $($_.Exception.Message)" "Warning"
                        throw
                    } else {
                        Write-Log "Folder already exists (concurrent creation): $part" "Debug" -VerboseOnly
                    }
                }
            }
        }

        # 最終確認（パス処理修正版）
        try {
            # パスの正規化と検証
            $cleanPath = $normalizedPath.Trim().TrimEnd('/')
            if ([string]::IsNullOrEmpty($cleanPath)) {
                Write-Log "Empty path after normalization, considering as success" "Debug" -VerboseOnly
                return $true
            }
            
            $finalFolder = Get-PnPFolder -Url $cleanPath -Includes ServerRelativeUrl -ErrorAction Stop
            if ($finalFolder) {
                Write-Log "Folder creation completed: $cleanPath" "Debug" -VerboseOnly
                return $true
            }
        }
        catch {
            # 最終検証に失敗した場合でも、フォルダ作成自体は成功している可能性があるため警告レベルに留める
            Write-Log "Final folder verification failed: $normalizedPath - $($_.Exception.Message)" "Debug" -VerboseOnly
            
            # フォルダ作成処理は成功したと見なす（SharePoint上でフォルダが実際に作成されている場合）
            return $true
        }

        return $true
    }
    catch {
        Write-Log "Failed to create folder: $FolderPath - $($_.Exception.Message)" "Warning"
        Write-Log "Error details: $($_.Exception.GetType().Name)" "Debug" -VerboseOnly
        return $false
    }
}

# Windows最適化ファイルアップロード関数（監視機能統合版）
function Upload-FilesToSharePoint {
    param(
        [array]$Files,
        [string]$SourceFilesDirectory,
        [string]$SharePointLibrary,
        [string]$TargetDirectory,
        [string]$RepositoryName,
        [string]$SourceDirectory
    )

    $totalFiles = $Files.Count
    $currentFile = 0
    $totalSuccessCount = 0
    $totalFailureCount = 0

    Write-Log "Starting upload of $totalFiles files to SharePoint..." "Info"

    # アップロード開始前の事前チェック
    $preCheck = Invoke-CrashProtection -PreProcessCheck
    if ($preCheck.RecommendedAction -eq "abort") {
        Write-Log "事前チェックにより処理を中止します: $($preCheck.Message)" "Error"
        return @{
            TotalSuccessCount = 0
            TotalFailureCount = $totalFiles
        }
    }

    # 開始時の詳細監視レポート
    $startReport = Invoke-SystemMonitoring -Force -DetailedReport
    Write-Log "=== ファイルアップロード開始時のシステム状態 ===" "Info"

    foreach ($file in $Files) {
        $currentFile++

        # 定期的なクラッシュ保護チェック
        $protectionResult = Invoke-CrashProtection -ProcessedFileCount $currentFile
        if ($protectionResult.ProtectionExecuted) {
            Write-Log "保護機能が作動しました: $($protectionResult.Message)" "Warning"

            if ($protectionResult.RecommendedAction -eq "pause") {
                Write-Log "処理を一時停止します（3秒）" "Warning"
                Start-Sleep -Seconds 3
            }
            elseif ($protectionResult.RecommendedAction -eq "abort") {
                Write-Log "安全のため処理を中止します" "Error"
                break
            }
        }

        # Windows進捗表示（監視情報付き）
        $percentComplete = [math]::Min([math]::Round(($currentFile / $totalFiles) * 100, 2), 100)
        $elapsedTime = (Get-Date) - $Global:StartTime

        if ($currentFile -gt 1) {
            $estimatedTotalTime = $elapsedTime.TotalSeconds / ($currentFile / $totalFiles)
            $remainingTime = [TimeSpan]::FromSeconds($estimatedTotalTime - $elapsedTime.TotalSeconds)
        }
        else {
            $remainingTime = [TimeSpan]::Zero
        }

        # 現在のメモリ使用量を進捗に含める
        $currentMemory = Start-MemoryMonitoring
        $memoryInfo = "メモリ: $($currentMemory.MemoryUsageMB)MB"

        Write-Progress -Activity "Uploading files to SharePoint ($memoryInfo)" `
            -Status "Processing file $currentFile of $totalFiles ($percentComplete%) - $($file.Name)" `
            -PercentComplete $percentComplete `
            -SecondsRemaining $remainingTime.TotalSeconds

        try {
            # 相対パス計算
            $relativePath = $file.FullName.Substring($SourceFilesDirectory.Length + 1)
            $fileName = Split-Path $relativePath -Leaf
            $folderPath = Split-Path $relativePath -Parent

            # ターゲットパス構築
            if (-not [string]::IsNullOrEmpty($folderPath) -and $folderPath -ne ".") {
                $targetFolderPath = $folderPath -replace "\\", "/"
                $targetPath = "$TargetDirectory/$targetFolderPath/$fileName" -Replace "\\", "/"
                $targetFolder = "$TargetDirectory/$targetFolderPath" -Replace "\\", "/"
            }
            else {
                $targetPath = "$TargetDirectory/$fileName" -Replace "\\", "/"
                $targetFolder = $TargetDirectory -Replace "\\", "/"
            }

            # SharePointフォルダの存在を確保
            if (-not (Ensure-SharePointFolder -FolderPath $targetPath -LibraryName $SharePointLibrary)) {
                throw "Failed to ensure SharePoint folder exists"
            }

            # SharePointにファイルをアップロード
            $sharePointFolderPath = "$SharePointLibrary/$targetFolder"
            try {
                # フォルダの存在を事前確認（ServerRelativeUrlを明示的に取得）
                try {
                    $targetFolderObj = Get-PnPFolder -Url $targetFolder -Includes ServerRelativeUrl -ErrorAction Stop
                } catch {
                    Write-Log "Target folder verification failed, but proceeding with upload: $targetFolder" "Debug" -VerboseOnly
                }
                
                # ファイルアップロード実行
                Add-PnPFile -Path $file.FullName -Folder $sharePointFolderPath -NewFileName $fileName -ErrorAction Stop
                Write-Log "File uploaded successfully: $fileName" "Debug" -VerboseOnly
            }
            catch {
                # ファイルが既に存在する場合、上書きを試行
                if ($_.Exception.Message -match "already exists|既にあります|already exist") {
                    try {
                        Write-Log "File already exists, attempting overwrite: $fileName" "Debug" -VerboseOnly
                        
                        # 既存のファイルを削除
                        $existingFilePath = "$targetFolder/$fileName"
                        Remove-PnPFile -Url $existingFilePath -Force -ErrorAction SilentlyContinue
                        
                        # 短い待機時間
                        Start-Sleep -Milliseconds 500
                        
                        # 再アップロード
                        Add-PnPFile -Path $file.FullName -Folder $sharePointFolderPath -NewFileName $fileName -ErrorAction Stop
                        Write-Log "File overwrite completed: $fileName" "Debug" -VerboseOnly
                    }
                    catch {
                        Write-Log "Failed to overwrite file: $fileName - $($_.Exception.Message)" "Warning"
                        throw
                    }
                } elseif ($_.Exception.Message -match "ServerRelativePath|has not been initialized") {
                    # ServerRelativePath問題の場合、再試行
                    try {
                        Write-Log "ServerRelativePath error detected, retrying with different approach: $fileName" "Warning"
                        
                        # 少し待機してから再試行
                        Start-Sleep -Seconds 1
                        
                        # フォルダを明示的に再取得
                        $null = Get-PnPFolder -Url $targetFolder -Includes ServerRelativeUrl -ErrorAction SilentlyContinue
                        
                        # 再アップロード
                        Add-PnPFile -Path $file.FullName -Folder $sharePointFolderPath -NewFileName $fileName -ErrorAction Stop
                        Write-Log "Retry upload successful: $fileName" "Success"
                    }
                    catch {
                        Write-Log "Retry failed for ServerRelativePath error: $fileName - $($_.Exception.Message)" "Error"
                        throw
                    }
                } else {
                    Write-Log "Upload failed with unhandled error: $fileName - $($_.Exception.Message)" "Error"
                    throw
                }
            }

            # 成功結果の記録
            $successResult = [PSCustomObject]@{
                FileName = $file.Name
                FilePath = $relativePath
                FileSize = $file.Length
                UploadTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                SharePointPath = $targetPath
                Repository = $RepositoryName
                SourceDirectory = $SourceDirectory
            }
            $Global:CurrentBatchSuccess += $successResult
            $totalSuccessCount++

            Write-Log "✓ Uploaded: $($file.Name)" "Debug" -VerboseOnly
        }
        catch {
            # 失敗結果の記録
            $failureResult = [PSCustomObject]@{
                FileName = $file.Name
                FilePath = $relativePath
                FileSize = $file.Length
                ErrorMessage = $_.Exception.Message
                AttemptTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                Repository = $RepositoryName
                SourceDirectory = $SourceDirectory
            }
            $Global:CurrentBatchFailure += $failureResult
            $totalFailureCount++

            Write-Log "✗ Failed to upload: $($file.Name) - $($_.Exception.Message)" "Error"
        }

        # バッチ処理出力チェック
        if (($Global:CurrentBatchSuccess.Count + $Global:CurrentBatchFailure.Count) -ge $BatchSize) {
            Export-BatchResults -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory -OutputPath $OutputPath
        }
    }

    Write-Progress -Activity "Uploading files to SharePoint" -Completed

    # 最終バッチ処理出力
    Export-BatchResults -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory -OutputPath $OutputPath -Force

    # アップロード完了時の詳細監視レポート
    $endReport = Invoke-SystemMonitoring -Force -DetailedReport
    Write-Log "=== ファイルアップロード完了時のシステム状態 ===" "Info"

    # 最終統計レポート
    Write-Log "--- アップロード統計 ---" "Success"
    Write-Log "処理ファイル数: $totalFiles" "Success"
    Write-Log "成功: $totalSuccessCount, 失敗: $totalFailureCount" "Success"
    Write-Log "最大メモリ使用量: $($Global:SystemMonitor.MaxMemoryUsageMB)MB" "Success"
    Write-Log "累計ネットワーク転送量: ↑$($endReport.Network.TotalUploadMB)MB ↓$($endReport.Network.TotalDownloadMB)MB" "Success"

    return @{
        TotalSuccessCount = $totalSuccessCount
        TotalFailureCount = $totalFailureCount
        MaxMemoryUsageMB = $Global:SystemMonitor.MaxMemoryUsageMB
        TotalUploadMB = $endReport.Network.TotalUploadMB
        TotalDownloadMB = $endReport.Network.TotalDownloadMB
    }
}

# Windows最適化並列アップロード関数（監視機能統合版）
function Upload-FilesToSharePointParallel {
    param(
        [array]$Files,
        [string]$SourceFilesDirectory,
        [string]$SharePointLibrary,
        [string]$TargetDirectory,
        [string]$RepositoryName,
        [string]$SourceDirectory,
        [string]$SharePointSiteUrl,
        [hashtable]$CertCredentials
    )

    $totalFiles = $Files.Count
    Write-Log "Starting parallel upload of $totalFiles files to SharePoint..." "Info"
    Write-Log "Using $MaxConcurrency concurrent threads" "Info"

    # 並列処理開始前のシステムリソースチェック
    $preCheck = Invoke-CrashProtection -PreProcessCheck
    if ($preCheck.RecommendedAction -eq "abort") {
        Write-Log "並列処理の事前チェックにより処理を中止します: $($preCheck.Message)" "Error"
        return @{
            TotalSuccessCount = 0
            TotalFailureCount = $totalFiles
        }
    }

    # リソース不足の場合は並行数を自動調整
    $resourceStatus = Test-SystemResources
    if (-not $resourceStatus.OverallOK) {
        $adjustedConcurrency = [math]::Max(1, [math]::Floor($MaxConcurrency / 2))
        Write-Log "リソース不足により並行処理数を調整: $MaxConcurrency → $adjustedConcurrency" "Warning"
        $script:MaxConcurrency = $adjustedConcurrency
    }

    # PowerShellバージョンをチェック、7+の場合はネイティブ並列処理を使用
    if ($PSVersionTable.PSVersion.Major -ge 7) {
        Write-Log "Using PowerShell 7+ native parallel processing" "Info"
        return Upload-FilesToSharePointParallelPS7 -Files $Files -SourceFilesDirectory $SourceFilesDirectory -SharePointLibrary $SharePointLibrary -TargetDirectory $TargetDirectory -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory
    }

    # PowerShell 5.1 - 順次処理にフォールバック
    Write-Log "PowerShell 5.1 detected, falling back to sequential processing for stability" "Warning"
    return Upload-FilesToSharePoint -Files $Files -SourceFilesDirectory $SourceFilesDirectory -SharePointLibrary $SharePointLibrary -TargetDirectory $TargetDirectory -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory
}

# PowerShell 7専用並列処理関数
function Upload-FilesToSharePointParallelPS7 {
    param(
        [array]$Files,
        [string]$SourceFilesDirectory,
        [string]$SharePointLibrary,
        [string]$TargetDirectory,
        [string]$RepositoryName,
        [string]$SourceDirectory
    )

    try {
        # 事前処理：必要なフォルダを一括作成（修正版）
        Write-Log "Pre-creating SharePoint folders..." "Info"
        $uniqueFolders = Get-UniqueFolders -Files $Files -SourceFilesDirectory $SourceFilesDirectory -TargetDirectory $TargetDirectory

        if ($uniqueFolders.Count -gt 0) {
            Write-Log "Found $($uniqueFolders.Count) unique folders to create" "Info"
            foreach ($folder in $uniqueFolders) {
                try {
                    Write-Log "Pre-creating folder: $folder" "Debug" -VerboseOnly
                    $null = Ensure-SharePointFolder -FolderPath "$folder/dummy.txt" -LibraryName $SharePointLibrary
                }
                catch {
                    Write-Log "Warning: Failed to pre-create folder $folder - will retry during upload" "Warning"
                }
            }
            Write-Log "Folder pre-creation completed" "Success"
        } else {
            Write-Log "No folders need to be pre-created" "Info"
        }

        # 修正された並列処理（チャンクベース）
        Write-Log "Starting chunk-based parallel processing..." "Info"

        # ファイルをチャンクに分割
        $chunks = @()
        for ($i = 0; $i -lt $Files.Count; $i += $ChunkSize) {
            $endIndex = [Math]::Min($i + $ChunkSize - 1, $Files.Count - 1)
            $chunk = $Files[$i..$endIndex]
            $chunks += ,@($chunk)
        }

        Write-Log "Processing $($Files.Count) files in $($chunks.Count) chunks with $MaxConcurrency concurrent threads..." "Info"

        # 並列処理でチャンクを処理
        $allResults = $chunks | ForEach-Object -ThrottleLimit $MaxConcurrency -Parallel {
            $chunkFiles = $_
            $sourceFilesDir = $using:SourceFilesDirectory
            $spLibrary = $using:SharePointLibrary
            $targetDir = $using:TargetDirectory
            $repoName = $using:RepositoryName
            $sourceDir = $using:SourceDirectory

            $chunkResults = @()

            # チャンク内の各ファイルを順次処理
            foreach ($file in $chunkFiles) {
                try {
                    # 相対パスの計算
                    $relativePath = $file.FullName.Substring($sourceFilesDir.Length + 1)

                    # ターゲットパスの構築
                    $fileName = Split-Path $relativePath -Leaf
                    $folderPath = Split-Path $relativePath -Parent

                    if (-not [string]::IsNullOrEmpty($folderPath) -and $folderPath -ne ".") {
                        $targetFolderPath = $folderPath -replace "\\", "/"
                        $targetPath = "$targetDir/$targetFolderPath/$fileName" -Replace "\\", "/"
                        $sharePointFolderPath = "$spLibrary/$targetDir/$targetFolderPath"
                    } else {
                        $targetPath = "$targetDir/$fileName" -Replace "\\", "/"
                        $sharePointFolderPath = "$spLibrary/$targetDir"
                    }

                    # ファイルをSharePointにアップロード
                    try {
                        # フォルダの存在を事前確認
                        $folderUrl = $targetFolderPath -replace "^$targetDir/", ""
                        if ($folderUrl) {
                            $null = Get-PnPFolder -Url $folderUrl -Includes ServerRelativeUrl -ErrorAction SilentlyContinue
                        }
                        
                        Add-PnPFile -Path $file.FullName -Folder $sharePointFolderPath -NewFileName $fileName -ErrorAction Stop
                    }
                    catch {
                        # ServerRelativePath問題の場合、再試行
                        if ($_.Exception.Message -match "ServerRelativePath|has not been initialized") {
                            Start-Sleep -Milliseconds 500
                            Add-PnPFile -Path $file.FullName -Folder $sharePointFolderPath -NewFileName $fileName -ErrorAction Stop
                        } else {
                            throw
                        }
                    }

                    # 成功結果を追加
                    $chunkResults += [PSCustomObject]@{
                        Success = $true
                        FileName = $file.Name
                        FilePath = $relativePath
                        FileSize = $file.Length
                        UploadTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                        SharePointPath = $targetPath
                        Repository = $repoName
                        SourceDirectory = $sourceDir
                        ErrorMessage = $null
                    }

                } catch {
                    # 失敗結果を追加
                    $chunkResults += [PSCustomObject]@{
                        Success = $false
                        FileName = $file.Name
                        FilePath = if ($relativePath) { $relativePath } else { "" }
                        FileSize = $file.Length
                        ErrorMessage = $_.Exception.Message
                        AttemptTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                        Repository = $repoName
                        SourceDirectory = $sourceDir
                        SharePointPath = ""
                        UploadTime = ""
                    }
                }
            }

            # チャンクの結果を返す
            return $chunkResults
        }

        # 全チャンクの結果を統合
        $results = @()
        foreach ($chunkResult in $allResults) {
            if ($chunkResult -is [array]) {
                $results += $chunkResult
            } else {
                $results += $chunkResult
            }
        }

        Write-Log "Received $($results.Count) results from parallel processing" "Info"
        Write-Log "Expected $($Files.Count) results" "Info"

        Write-Log "Parallel processing completed" "Success"

        # 結果の処理
        $totalSuccessCount = 0
        $totalFailureCount = 0

        # 結果を直接処理
        foreach ($result in $results) {
            if ($result -and $result.PSObject.Properties.Name -contains "Success") {
                if ($result.Success) {
                    $Global:CurrentBatchSuccess += $result
                    $totalSuccessCount++
                    Write-Log "✓ Uploaded: $($result.FileName)" "Debug" -VerboseOnly
                } else {
                    $Global:CurrentBatchFailure += $result
                    $totalFailureCount++

                    $errorMsg = if ($result.ErrorMessage) { $result.ErrorMessage } else { "Unknown error" }
                    $fileName = if ($result.FileName) { $result.FileName } else { "Unknown file" }
                    Write-Log "✗ Failed to upload: $fileName - $errorMsg" "Warning"
                }
            } else {
                Write-Log "Invalid result object detected and skipped" "Debug" -VerboseOnly
            }
        }

        Write-Log "Results summary: Success=$totalSuccessCount, Failures=$totalFailureCount" "Info"

        # バッチサイズチェック（必要な場合のみ出力）
        if (($Global:CurrentBatchSuccess.Count + $Global:CurrentBatchFailure.Count) -ge $BatchSize) {
            Export-BatchResults -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory -OutputPath $OutputPath
        }

        # 最終バッチの出力
        Export-BatchResults -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory -OutputPath $OutputPath -Force

        Write-Log "Parallel upload completed - Success: $totalSuccessCount, Failures: $totalFailureCount" "Success"

        return @{
            TotalSuccessCount = $totalSuccessCount
            TotalFailureCount = $totalFailureCount
        }

    } catch {
        Write-Log "Error during parallel processing: $($_.Exception.Message)" "Error"
        Write-Log "Error details: $($_.Exception.GetType().Name)" "Error"
        Write-Log "Falling back to sequential processing..." "Warning"

        # 並列処理が失敗した場合、シーケンシャル処理にフォールバック
        return Upload-FilesToSharePoint -Files $Files -SourceFilesDirectory $SourceFilesDirectory -SharePointLibrary $SharePointLibrary -TargetDirectory $TargetDirectory -RepositoryName $RepositoryName -SourceDirectory $SourceDirectory
    }
}

# ユニークフォルダ取得のヘルパー関数（パス処理修正版）
function Get-UniqueFolders {
    param(
        [array]$Files,
        [string]$SourceFilesDirectory,
        [string]$TargetDirectory
    )

    $uniqueFolders = @{}
    foreach ($file in $Files) {
        try {
            $relativePath = $file.FullName.Substring($SourceFilesDirectory.Length + 1)
            $folderPath = Split-Path $relativePath -Parent

            if (-not [string]::IsNullOrEmpty($folderPath) -and $folderPath -ne ".") {
                $targetFolderPath = $folderPath -replace "\\", "/"
                # パス結合時の引用符問題を修正
                $targetPath = "$TargetDirectory/$targetFolderPath" -Replace "\\", "/"
                # 末尾のダミーファイル名を削除
                $targetPath = $targetPath.TrimEnd('/')
                
                if (-not $uniqueFolders.ContainsKey($targetPath)) {
                    $uniqueFolders[$targetPath] = $true
                    Write-Log "Added unique folder: $targetPath" "Debug" -VerboseOnly
                }
            }
        }
        catch {
            Write-Log "Error processing file path: $($file.FullName) - $($_.Exception.Message)" "Warning"
        }
    }

    $sortedFolders = $uniqueFolders.Keys | Sort-Object { $_.Length }
    Write-Log "Found $($sortedFolders.Count) unique folders to pre-create" "Debug" -VerboseOnly
    return $sortedFolders
}

# バッチ処理結果出力関数
function Export-BatchResults {
    param(
        [string]$RepositoryName,
        [string]$SourceDirectory,
        [string]$OutputPath,
        [switch]$Force
    )

    try {
        $shouldExport = $Force -or
                       ($Global:CurrentBatchSuccess.Count -ge $BatchSize) -or
                       ($Global:CurrentBatchFailure.Count -ge $BatchSize)

        if (-not $shouldExport) {
            return $true
        }

        # 出力ディレクトリの作成
        if (-not (Test-Path $OutputPath)) {
            New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
        }

        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $safeRepoName = $RepositoryName -replace '[\\/:*?"<>|]', '_'
        $safeDirName = ($SourceDirectory -split '[\\\/]')[-1] -replace '[\\/:*?"<>|]', '_'
        $Global:BatchCounter++

        # 成功結果CSV出力
        if ($Global:CurrentBatchSuccess.Count -gt 0) {
            $successFile = Join-Path $OutputPath "success_${safeRepoName}_${safeDirName}_batch${Global:BatchCounter}_${timestamp}.csv"
            $Global:CurrentBatchSuccess | Select-Object FilePath, FileSize, UploadTime, SharePointPath, Repository, SourceDirectory |
                Export-Csv -Path $successFile -NoTypeInformation -Encoding UTF8
            Write-Log "Success batch exported: $successFile ($($Global:CurrentBatchSuccess.Count) files)" "Success"
            $Global:CurrentBatchSuccess = @()
        }

        # 失敗結果CSV出力
        if ($Global:CurrentBatchFailure.Count -gt 0) {
            $failureFile = Join-Path $OutputPath "failure_${safeRepoName}_${safeDirName}_batch${Global:BatchCounter}_${timestamp}.csv"
            $Global:CurrentBatchFailure | Select-Object FilePath, FileSize, @{Name="UploadTime"; Expression={$_.AttemptTime}}, ErrorMessage, Repository, SourceDirectory |
                Export-Csv -Path $failureFile -NoTypeInformation -Encoding UTF8
            Write-Log "Failure batch exported: $failureFile ($($Global:CurrentBatchFailure.Count) files)" "Warning"
            $Global:CurrentBatchFailure = @()
        }

        return $true
    }
    catch {
        Write-Log "Failed to export batch results: $($_.Exception.Message)" "Error"
        return $false
    }
}

# 処理状態チェック関数
function Test-ProcessedRepository {
    param(
        [string]$RepositoryName,
        [string]$SourceDirectory
    )

    if (-not $ResumeMode -or -not (Test-Path $Global:ProcessedReposFile)) {
        return $false
    }

    $processedKey = "$RepositoryName|$SourceDirectory"
    $processedRepos = Get-Content $Global:ProcessedReposFile -ErrorAction SilentlyContinue

    return $processedRepos -contains $processedKey
}

# 処理状態記録関数
function Add-ProcessedRepository {
    param(
        [string]$RepositoryName,
        [string]$SourceDirectory
    )

    $processedKey = "$RepositoryName|$SourceDirectory"
    Add-Content -Path $Global:ProcessedReposFile -Value $processedKey -Encoding UTF8
}

# 安全なSharePoint切断
function Disconnect-SharePointSafely {
    try {
        $connection = Get-PnPConnection -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Log "Disconnecting from SharePoint..." "Info"
            Disconnect-PnPOnline -ErrorAction SilentlyContinue
            Write-Log "SharePoint connection closed" "Success"
        }
    }
    catch {
        Write-Log "Warning during SharePoint disconnection: $($_.Exception.Message)" "Warning"
    }
}

# システム監視機能：最終監視レポート生成
function Export-SystemMonitoringReport {
    <#
    .SYNOPSIS
    システム監視の最終レポートを生成してファイルに出力

    .DESCRIPTION
    スクリプト実行中に収集されたすべての監視データを統合し、
    詳細なレポートを生成してファイルに出力する
    #>

    param(
        [string]$OutputPath = ".\output",
        [int]$TotalProcessedFiles = 0,
        [int]$TotalSuccessCount = 0,
        [int]$TotalFailureCount = 0
    )

    try {
        # 出力ディレクトリの作成
        if (-not (Test-Path $OutputPath)) {
            New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
        }

        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $reportFile = Join-Path $OutputPath "system-monitoring-report_${timestamp}.txt"

        # 最終監視データの取得
        $finalReport = Invoke-SystemMonitoring -Force
        $totalElapsed = (Get-Date) - $Global:StartTime

        # レポート内容の生成
        $reportContent = @"
=== システム監視レポート ===
生成日時: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
スクリプト実行時間: $("{0:hh}:{0:mm}:{0:ss}" -f $totalElapsed)

=== 処理統計 ===
総処理ファイル数: $TotalProcessedFiles
成功: $TotalSuccessCount
失敗: $TotalFailureCount
成功率: $(if($TotalProcessedFiles -gt 0){[math]::Round(($TotalSuccessCount / $TotalProcessedFiles) * 100, 2)}else{0})%

=== メモリ使用統計 ===
最大メモリ使用量: $($Global:SystemMonitor.MaxMemoryUsageMB)MB
メモリ閾値: $($Global:SystemMonitor.MemoryThresholdMB)MB
閾値超過率: $(if($Global:SystemMonitor.MaxMemoryUsageMB -gt 0){[math]::Round(($Global:SystemMonitor.MaxMemoryUsageMB / $Global:SystemMonitor.MemoryThresholdMB) * 100, 2)}else{0})%

=== ネットワーク転送統計 ===
累計アップロード量: $($finalReport.Network.TotalUploadMB)MB
累計ダウンロード量: $($finalReport.Network.TotalDownloadMB)MB
最終アップロード速度: $($finalReport.Network.UploadSpeedMbps)Mbps
最終ダウンロード速度: $($finalReport.Network.DownloadSpeedMbps)Mbps

=== システムリソース状態 ===
ディスク容量: $(if($finalReport.Resources.DiskSpaceOK){'正常'}else{'警告'})
システムメモリ: $(if($finalReport.Resources.MemoryOK){'正常'}else{'警告'})
総合状態: $(if($finalReport.Resources.OverallOK){'正常'}else{'要注意'})

=== ヘルスチェック統計 ===
実行回数: $($Global:SystemMonitor.HealthCheckCounter)
監視間隔: $($Global:SystemMonitor.MemoryCheckInterval)秒

=== 推奨事項 ===
"@

        # 推奨事項の追加
        if ($finalReport.Resources.Recommendations.Count -gt 0) {
            foreach ($recommendation in $finalReport.Resources.Recommendations) {
                $reportContent += "`n- $recommendation"
            }
        } else {
            $reportContent += "`n- システムは正常に動作しています"
        }

        # レポートファイルの出力
        $reportContent | Out-File -FilePath $reportFile -Encoding UTF8

        Write-Log "システム監視レポートを生成しました: $reportFile" "Success"
        return $reportFile
    }
    catch {
        Write-Log "監視レポート生成エラー: $($_.Exception.Message)" "Error"
        return $null
    }
}

# メイン処理関数（監視機能統合版）
function Start-LocalToSharePointUpload {
    try {
        Write-Log "=== Local Directory to SharePoint Upload Script Started (Windows Optimized with System Monitoring) ===" "Info"
        Write-Log "Start Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
        Write-Log "PowerShell Version: $($PSVersionTable.PSVersion)" "Info"
        Write-Log "Batch Size: $BatchSize files" "Info"
        Write-Log "Max Concurrency: $MaxConcurrency threads" "Info"
        Write-Log "Chunk Size: $ChunkSize files per batch" "Info"

        # システム監視機能の初期化
        Write-Log "=== システム監視機能を初期化中 ===" "Info"
        Write-Log "メモリ閾値: $($Global:SystemMonitor.MemoryThresholdMB)MB" "Info"
        Write-Log "監視間隔: $($Global:SystemMonitor.MemoryCheckInterval)秒" "Info"
        Write-Log "最小ディスク容量: $($Global:SystemMonitor.MinDiskSpaceGB)GB" "Info"
        Write-Log "最小システムメモリ: $($Global:SystemMonitor.MinAvailableMemoryGB)GB" "Info"
        Write-Log "監視ログファイル: $($Global:SystemMonitor.MonitorLogFile)" "Info"

        # 初期システム状態チェック
        $initialCheck = Invoke-SystemMonitoring -Force -DetailedReport
        Write-Log "=== 初期システム状態チェック完了 ===" "Info"

        # 証明書認証情報の読み取り
        $credentials = Read-CertificateCredentials -FilePath $CredentialsFile

        # 設定ファイルの読み取り
        $configurations = Read-ConfigurationFile -FilePath $ConfigFile
        if ($configurations.Count -eq 0) {
            throw "No valid configurations found in config file"
        }

        Write-Log "Found $($configurations.Count) repository configurations" "Info"

        $totalConfigs = $configurations.Count
        $currentConfig = 0
        $overallSuccessCount = 0
        $overallFailureCount = 0
        $overallProcessedFiles = 0

        foreach ($config in $configurations) {
            $currentConfig++

            Write-Log "--- Processing Configuration $currentConfig/$totalConfigs : $($config.RepositoryName) ---" "Info"
            Write-Log "Repository: $($config.RepositoryName)" "Info"
            Write-Log "Source Directory: $($config.SourceDirectory)" "Info"
            Write-Log "SharePoint Site: $($config.SharePointSiteUrl)" "Info"
            Write-Log "Target Directory: $($config.TargetDirectory)" "Info"

            # 処理済みかどうかをチェック
            if (Test-ProcessedRepository -RepositoryName $config.RepositoryName -SourceDirectory $config.SourceDirectory) {
                Write-Log "Repository already processed, skipping..." "Warning"
                continue
            }

            # バッチ処理カウンターのリセット
            $Global:BatchCounter = 0
            $Global:CurrentBatchSuccess = @()
            $Global:CurrentBatchFailure = @()

            try {
                # SharePointに接続
                if (-not (Connect-SharePointSite -SiteUrl $config.SharePointSiteUrl -CertCredentials $credentials)) {
                    throw "Failed to connect to SharePoint site"
                }

                # ソースファイルの取得
                $sourceResult = Get-SourceFiles -RepositoryName $config.RepositoryName -SourceDirectory $config.SourceDirectory
                if (-not $sourceResult.Success) {
                    throw "Failed to get files from source directory"
                }

                Write-Log "Retrieved $($sourceResult.Files.Count) files from source directory" "Info"

                # ファイルをSharePointにアップロード
                $useParallel = $MaxConcurrency -gt 1 -and $sourceResult.Files.Count -gt $ChunkSize -and $PSVersionTable.PSVersion.Major -ge 7

                # SharePointにファイルをアップロード
                if ($MaxConcurrency -gt 1 -and $sourceResult.Files.Count -gt $ChunkSize) {
                    Write-Log "Using parallel upload with $MaxConcurrency concurrent threads" "Info"
                    $uploadResult = Upload-FilesToSharePointParallel -Files $sourceResult.Files `
                        -SourceFilesDirectory $sourceResult.SourceDirectory `
                        -SharePointLibrary $config.SharePointLibrary `
                        -TargetDirectory $config.TargetDirectory `
                        -RepositoryName $config.RepositoryName `
                        -SourceDirectory $config.SourceDirectory `
                        -SharePointSiteUrl $config.SharePointSiteUrl `
                        -CertCredentials $credentials
                }
                else {
                    Write-Log "Using sequential upload" "Info"
                    $uploadResult = Upload-FilesToSharePoint -Files $sourceResult.Files `
                        -SourceFilesDirectory $sourceResult.SourceDirectory `
                        -SharePointLibrary $config.SharePointLibrary `
                        -TargetDirectory $config.TargetDirectory `
                        -RepositoryName $config.RepositoryName `
                        -SourceDirectory $config.SourceDirectory
                }

                # 統計の更新
                $overallSuccessCount += $uploadResult.TotalSuccessCount
                $overallFailureCount += $uploadResult.TotalFailureCount
                $overallProcessedFiles += $sourceResult.Files.Count

                Write-Log "Configuration completed - Success: $($uploadResult.TotalSuccessCount), Failures: $($uploadResult.TotalFailureCount)" "Success"

                # 設定完了後の監視レポート
                $configReport = Invoke-SystemMonitoring -Force
                Write-Log "設定 $currentConfig/$totalConfigs 完了後のシステム状態:" "Info"
                Write-Log "  メモリ使用量: $($configReport.Memory.MemoryUsageMB)MB" "Info"
                Write-Log "  ネットワーク転送量: ↑$($configReport.Network.TotalUploadMB)MB" "Info"

                # 処理済みとして記録
                Add-ProcessedRepository -RepositoryName $config.RepositoryName -SourceDirectory $config.SourceDirectory

                # 一時ディレクトリのクリーンアップ（ソースディレクトリは削除しない）
                if ($sourceResult.IsTemporary -and $sourceResult.SourceDirectory -and (Test-Path $sourceResult.SourceDirectory)) {
                    Remove-Item -Path $sourceResult.SourceDirectory -Recurse -Force -ErrorAction SilentlyContinue
                    Write-Log "Cleaned up temp directory: $($sourceResult.SourceDirectory)" "Info"
                } else {
                    Write-Log "Source directory preserved (not temporary): $($sourceResult.SourceDirectory)" "Debug" -VerboseOnly
                }
            }
            catch {
                Write-Log "Failed to process configuration: $($_.Exception.Message)" "Error"

                # エラー時のクリーンアップ（ソースディレクトリは削除しない）
                if ($sourceResult.IsTemporary -and $sourceResult.SourceDirectory -and (Test-Path $sourceResult.SourceDirectory)) {
                    Remove-Item -Path $sourceResult.SourceDirectory -Recurse -Force -ErrorAction SilentlyContinue
                }
                continue
            }
            finally {
                # SharePoint接続の切断
                Disconnect-SharePointSafely
            }
        }

        # 最終結果と監視レポート生成
        Write-Log "=== Upload Process Completed ===" "Info"
        Write-Log "Total Configurations Processed: $currentConfig" "Info"
        Write-Log "Total Files Processed: $overallProcessedFiles" "Info"
        Write-Log "Overall Success Count: $overallSuccessCount" "Success"
        Write-Log "Overall Failure Count: $overallFailureCount" "Error"
        Write-Log "End Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"

        $totalElapsed = (Get-Date) - $Global:StartTime
        $elapsedString = "{0:hh}:{0:mm}:{0:ss}" -f $totalElapsed
        Write-Log "Total Elapsed Time: $elapsedString" "Success"

        # 最終システム監視レポート
        Write-Log "=== 最終システム監視レポート生成中 ===" "Info"
        $finalMonitoringReport = Invoke-SystemMonitoring -Force -DetailedReport

        # 詳細監視レポートファイルの生成
        $reportFile = Export-SystemMonitoringReport -OutputPath $OutputPath -TotalProcessedFiles $overallProcessedFiles -TotalSuccessCount $overallSuccessCount -TotalFailureCount $overallFailureCount

        if ($reportFile) {
            Write-Log "詳細な監視レポートが生成されました: $reportFile" "Success"
        }

        # 最終統計サマリー
        Write-Log "=== 最終統計サマリー ===" "Success"
        Write-Log "処理効率: $(if($overallProcessedFiles -gt 0){[math]::Round(($overallSuccessCount / $overallProcessedFiles) * 100, 2)}else{0})%" "Success"
        Write-Log "最大メモリ使用量: $($Global:SystemMonitor.MaxMemoryUsageMB)MB / $($Global:SystemMonitor.MemoryThresholdMB)MB" "Success"
        Write-Log "ヘルスチェック実行回数: $($Global:SystemMonitor.HealthCheckCounter)" "Success"
        Write-Log "累計ネットワーク転送量: ↑$($finalMonitoringReport.Network.TotalUploadMB)MB ↓$($finalMonitoringReport.Network.TotalDownloadMB)MB" "Success"
    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" "Error"
        exit 1
    }
    finally {
        Disconnect-SharePointSafely
    }
}

# スクリプト実行
if ($MyInvocation.InvocationName -ne '.') {
    Start-LocalToSharePointUpload
}
